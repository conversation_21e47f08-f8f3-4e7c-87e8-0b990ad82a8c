<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#333" stroke-width="2"/>
  
  <!-- Chart/Statistics icon -->
  <rect x="30" y="70" width="8" height="20" fill="white" opacity="0.9"/>
  <rect x="42" y="60" width="8" height="30" fill="white" opacity="0.9"/>
  <rect x="54" y="50" width="8" height="40" fill="white" opacity="0.9"/>
  <rect x="66" y="45" width="8" height="45" fill="white" opacity="0.9"/>
  <rect x="78" y="55" width="8" height="35" fill="white" opacity="0.9"/>
  <rect x="90" y="65" width="8" height="25" fill="white" opacity="0.9"/>
  
  <!-- AI/Augment symbol -->
  <circle cx="64" cy="35" r="8" fill="white" opacity="0.9"/>
  <text x="64" y="40" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10" font-weight="bold">AI</text>
</svg>
